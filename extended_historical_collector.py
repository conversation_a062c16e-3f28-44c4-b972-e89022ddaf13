"""
Extended Historical Cryptocurrency Data Collector
Collects 12-13 years of historical data from multiple sources (2011-2024)
"""

import os
import pandas as pd
import requests
import time
import logging
from datetime import datetime, timedelta
from tqdm import tqdm
import json
import csv
from io import StringIO
import zipfile
import tempfile
import gzip

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExtendedHistoricalCollector:
    def __init__(self):
        self.data_dir = "data"
        self.extended_dir = f"{self.data_dir}/extended_historical"
        self.processed_dir = f"{self.data_dir}/processed"
        
        # Create directories
        os.makedirs(self.extended_dir, exist_ok=True)
        os.makedirs(self.processed_dir, exist_ok=True)
        
        # Extended date range for 12-13 years
        self.start_date = datetime(2011, 1, 1)  # Bitcoin's early trading days
        self.end_date = datetime(2024, 10, 3)
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def collect_bitcoincharts_data(self):
        """Collect historical Bitcoin data from bitcoincharts.com"""
        logger.info("Collecting Bitcoin historical data from bitcoincharts.com...")
        
        # Major exchanges that have historical data from 2011-2013
        exchanges = [
            'bitstampUSD',  # Available from 2011
            'mtgoxUSD',     # Historical data (defunct but has early data)
            'btceUSD',      # Historical data (defunct but has early data)
            'coinbaseUSD'   # Available from 2012
        ]
        
        all_data = []
        
        for exchange in exchanges:
            try:
                logger.info(f"Downloading data from {exchange}...")
                url = f"http://api.bitcoincharts.com/v1/csv/{exchange}.csv.gz"

                response = self.session.get(url, timeout=300)
                if response.status_code == 200:
                    # Save raw data
                    filename = f"{self.extended_dir}/{exchange}_raw.csv.gz"
                    with open(filename, 'wb') as f:
                        f.write(response.content)

                    # Process the data (bitcoincharts format: timestamp, price, volume)
                    with gzip.open(filename, 'rt') as f:
                        df = pd.read_csv(f, names=['timestamp', 'price', 'volume'])

                    # Convert timestamp to datetime
                    df['date'] = pd.to_datetime(df['timestamp'], unit='s')
                    df['exchange'] = exchange
                    df['symbol'] = 'BTC'

                    # Filter to our date range
                    df = df[(df['date'] >= self.start_date) & (df['date'] <= self.end_date)]

                    if not df.empty:
                        all_data.append(df)
                        logger.info(f"Collected {len(df)} records from {exchange}")

                    time.sleep(2)  # Rate limiting

            except Exception as e:
                logger.warning(f"Failed to collect from {exchange}: {str(e)}")
                continue
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            output_file = f"{self.processed_dir}/bitcoincharts_historical_data.csv"
            combined_df.to_csv(output_file, index=False)
            logger.info(f"Saved bitcoincharts data: {len(combined_df)} records to {output_file}")
            return combined_df
        
        return pd.DataFrame()
    
    def collect_early_altcoin_data(self):
        """Collect early altcoin data from various sources"""
        logger.info("Collecting early altcoin historical data...")
        
        # Early altcoins that existed in 2011-2013
        early_altcoins = {
            'LTC': 'Litecoin',      # Launched October 2011
            'NMC': 'Namecoin',      # Launched April 2011
            'PPC': 'Peercoin',      # Launched August 2012
            'XRP': 'Ripple',        # Launched 2012
            'DOGE': 'Dogecoin',     # Launched December 2013
        }
        
        all_data = []
        
        # Try to get data from CryptoCompare for early altcoins
        for symbol, name in early_altcoins.items():
            try:
                logger.info(f"Collecting historical data for {symbol} ({name})...")
                
                # CryptoCompare historical daily data
                url = "https://min-api.cryptocompare.com/data/v2/histoday"
                params = {
                    'fsym': symbol,
                    'tsym': 'USD',
                    'limit': 2000,  # Maximum allowed
                    'toTs': int(self.end_date.timestamp())
                }
                
                response = self.session.get(url, params=params)
                if response.status_code == 200:
                    data = response.json()
                    
                    if data.get('Response') == 'Success' and data.get('Data', {}).get('Data'):
                        records = data['Data']['Data']
                        df = pd.DataFrame(records)
                        
                        # Convert timestamp to datetime
                        df['date'] = pd.to_datetime(df['time'], unit='s')
                        df['symbol'] = symbol
                        df['name'] = name
                        df['source'] = 'CryptoCompare'
                        
                        # Filter to our extended date range
                        df = df[(df['date'] >= self.start_date) & (df['date'] <= self.end_date)]
                        
                        if not df.empty:
                            all_data.append(df)
                            logger.info(f"Collected {len(df)} records for {symbol}")
                
                time.sleep(1.5)  # Rate limiting for free tier
                
            except Exception as e:
                logger.warning(f"Failed to collect data for {symbol}: {str(e)}")
                continue
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            output_file = f"{self.processed_dir}/early_altcoins_historical_data.csv"
            combined_df.to_csv(output_file, index=False)
            logger.info(f"Saved early altcoins data: {len(combined_df)} records to {output_file}")
            return combined_df
        
        return pd.DataFrame()
    
    def collect_kaggle_bitcoin_historical(self):
        """Collect the famous Kaggle Bitcoin historical dataset"""
        logger.info("Attempting to collect Kaggle Bitcoin historical dataset...")
        
        try:
            # Try to use kaggle API if available
            import kaggle
            
            # Download the comprehensive Bitcoin dataset
            dataset_name = 'mczielinski/bitcoin-historical-data'
            
            logger.info(f"Downloading Kaggle dataset: {dataset_name}")
            kaggle.api.dataset_download_files(
                dataset_name,
                path=f"{self.extended_dir}/kaggle_bitcoin",
                unzip=True
            )
            
            # Process the downloaded files
            kaggle_dir = f"{self.extended_dir}/kaggle_bitcoin"
            all_data = []
            
            if os.path.exists(kaggle_dir):
                for file in os.listdir(kaggle_dir):
                    if file.endswith('.csv'):
                        filepath = os.path.join(kaggle_dir, file)
                        try:
                            df = pd.read_csv(filepath)
                            
                            # Standardize column names
                            if 'Timestamp' in df.columns:
                                df['date'] = pd.to_datetime(df['Timestamp'], unit='s')
                            elif 'Date' in df.columns:
                                df['date'] = pd.to_datetime(df['Date'])
                            
                            df['source'] = f'Kaggle_{file}'
                            df['symbol'] = 'BTC'
                            
                            # Filter to our date range
                            if 'date' in df.columns:
                                df = df[(df['date'] >= self.start_date) & (df['date'] <= self.end_date)]
                                
                                if not df.empty:
                                    all_data.append(df)
                                    logger.info(f"Processed {file}: {len(df)} records")
                        
                        except Exception as e:
                            logger.warning(f"Failed to process {file}: {str(e)}")
                            continue
            
            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)
                output_file = f"{self.processed_dir}/kaggle_bitcoin_historical_data.csv"
                combined_df.to_csv(output_file, index=False)
                logger.info(f"Saved Kaggle Bitcoin data: {len(combined_df)} records to {output_file}")
                return combined_df
        
        except ImportError:
            logger.warning("Kaggle library not available. Install with: pip install kaggle")
            logger.info("Manual download: https://www.kaggle.com/datasets/mczielinski/bitcoin-historical-data")
        except Exception as e:
            logger.error(f"Error collecting Kaggle data: {str(e)}")
        
        return pd.DataFrame()
    
    def collect_github_datasets(self):
        """Collect cryptocurrency datasets from GitHub repositories"""
        logger.info("Collecting cryptocurrency datasets from GitHub repositories...")
        
        # Known GitHub repositories with historical crypto data
        github_datasets = [
            {
                'name': 'crypto2',
                'url': 'https://raw.githubusercontent.com/sstoeckl/crypto2/main/data/crypto_history.csv',
                'description': 'Comprehensive crypto market data'
            },
            {
                'name': 'bitcoin_data',
                'url': 'https://raw.githubusercontent.com/datasets/bitcoin/master/data/bitcoin.csv',
                'description': 'Bitcoin price data'
            }
        ]
        
        all_data = []
        
        for dataset in github_datasets:
            try:
                logger.info(f"Downloading {dataset['name']}: {dataset['description']}")
                
                response = self.session.get(dataset['url'])
                if response.status_code == 200:
                    # Try to parse as CSV
                    df = pd.read_csv(StringIO(response.text))
                    
                    # Add metadata
                    df['source'] = f"GitHub_{dataset['name']}"
                    
                    # Try to identify date column
                    date_columns = ['date', 'Date', 'timestamp', 'Timestamp', 'time']
                    for col in date_columns:
                        if col in df.columns:
                            try:
                                df['date'] = pd.to_datetime(df[col])
                                break
                            except:
                                continue
                    
                    # Filter to our date range if date column found
                    if 'date' in df.columns:
                        df = df[(df['date'] >= self.start_date) & (df['date'] <= self.end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                        logger.info(f"Collected {len(df)} records from {dataset['name']}")
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                logger.warning(f"Failed to collect from {dataset['name']}: {str(e)}")
                continue
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            output_file = f"{self.processed_dir}/github_datasets_historical_data.csv"
            combined_df.to_csv(output_file, index=False)
            logger.info(f"Saved GitHub datasets: {len(combined_df)} records to {output_file}")
            return combined_df
        
        return pd.DataFrame()

    def collect_wayback_machine_cmc_data(self):
        """Collect early CoinMarketCap data from Wayback Machine archives"""
        logger.info("Collecting early CoinMarketCap data from Wayback Machine...")

        # Key dates when CMC had snapshots in early years
        wayback_dates = [
            '20130428',  # CMC launch (April 28, 2013)
            '20130630',  # Q2 2013
            '20130930',  # Q3 2013
            '20131231',  # End of 2013
            '20140331',  # Q1 2014
            '20140630',  # Q2 2014
            '20140930',  # Q3 2014
            '20141231',  # End of 2014
        ]

        all_data = []

        for date in wayback_dates:
            try:
                logger.info(f"Attempting to collect CMC data from {date}...")

                # Wayback Machine URL format
                wayback_url = f"https://web.archive.org/web/{date}/http://coinmarketcap.com/"

                response = self.session.get(wayback_url, timeout=30)
                if response.status_code == 200:
                    # Try to parse the historical CMC page
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Look for cryptocurrency data in tables
                    tables = soup.find_all('table')

                    for table in tables:
                        try:
                            # Convert table to DataFrame
                            df = pd.read_html(str(table))[0]

                            if len(df.columns) >= 4:  # Should have name, price, market cap, etc.
                                df['snapshot_date'] = pd.to_datetime(date, format='%Y%m%d')
                                df['source'] = f'Wayback_CMC_{date}'

                                all_data.append(df)
                                logger.info(f"Collected {len(df)} records from {date}")
                                break

                        except Exception as e:
                            continue

                time.sleep(5)  # Be respectful to Wayback Machine

            except Exception as e:
                logger.warning(f"Failed to collect Wayback data for {date}: {str(e)}")
                continue

        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            output_file = f"{self.processed_dir}/wayback_cmc_historical_data.csv"
            combined_df.to_csv(output_file, index=False)
            logger.info(f"Saved Wayback CMC data: {len(combined_df)} records to {output_file}")
            return combined_df

        return pd.DataFrame()

    def collect_blockchain_info_data(self):
        """Collect Bitcoin data from blockchain.info historical charts"""
        logger.info("Collecting Bitcoin data from blockchain.info...")

        try:
            # blockchain.info chart data endpoints
            chart_types = [
                'market-price',      # Bitcoin price
                'market-cap',        # Market capitalization
                'trade-volume',      # Trading volume
                'n-transactions',    # Number of transactions
            ]

            all_data = []

            for chart_type in chart_types:
                try:
                    logger.info(f"Collecting {chart_type} data from blockchain.info...")

                    # blockchain.info API endpoint
                    url = f"https://api.blockchain.info/charts/{chart_type}"
                    params = {
                        'timespan': 'all',
                        'format': 'json'
                    }

                    response = self.session.get(url, params=params)
                    if response.status_code == 200:
                        data = response.json()

                        if 'values' in data:
                            records = []
                            for point in data['values']:
                                records.append({
                                    'date': pd.to_datetime(point['x'], unit='s'),
                                    'value': point['y'],
                                    'metric': chart_type,
                                    'symbol': 'BTC',
                                    'source': 'blockchain.info'
                                })

                            df = pd.DataFrame(records)

                            # Filter to our date range
                            df = df[(df['date'] >= self.start_date) & (df['date'] <= self.end_date)]

                            if not df.empty:
                                all_data.append(df)
                                logger.info(f"Collected {len(df)} {chart_type} records")

                    time.sleep(2)  # Rate limiting

                except Exception as e:
                    logger.warning(f"Failed to collect {chart_type}: {str(e)}")
                    continue

            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)
                output_file = f"{self.processed_dir}/blockchain_info_historical_data.csv"
                combined_df.to_csv(output_file, index=False)
                logger.info(f"Saved blockchain.info data: {len(combined_df)} records to {output_file}")
                return combined_df

        except Exception as e:
            logger.error(f"Error collecting blockchain.info data: {str(e)}")

        return pd.DataFrame()

    def collect_all_extended_sources(self):
        """Collect data from all extended historical sources"""
        logger.info("Starting comprehensive extended historical data collection...")
        logger.info(f"Date range: {self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}")

        all_datasets = []
        collection_summary = []

        # Collect from each extended source
        sources = [
            ("Bitcoincharts.com", self.collect_bitcoincharts_data),
            ("Early Altcoins", self.collect_early_altcoin_data),
            ("Kaggle Bitcoin Dataset", self.collect_kaggle_bitcoin_historical),
            ("GitHub Repositories", self.collect_github_datasets),
            ("Wayback Machine CMC", self.collect_wayback_machine_cmc_data),
            ("Blockchain.info", self.collect_blockchain_info_data),
        ]

        for source_name, collect_func in sources:
            try:
                logger.info(f"\n{'='*50}")
                logger.info(f"Collecting from: {source_name}")
                logger.info(f"{'='*50}")

                df = collect_func()

                if not df.empty:
                    all_datasets.append(df)
                    collection_summary.append(f"{source_name}: {len(df)} records")
                    logger.info(f"✓ {source_name}: Successfully collected {len(df)} records")
                else:
                    collection_summary.append(f"{source_name}: No data collected")
                    logger.warning(f"✗ {source_name}: No data collected")

            except Exception as e:
                error_msg = f"{source_name}: Error - {str(e)}"
                collection_summary.append(error_msg)
                logger.error(f"✗ {error_msg}")
                continue

        # Combine all datasets
        if all_datasets:
            logger.info(f"\n{'='*50}")
            logger.info("Combining all extended historical datasets...")
            logger.info(f"{'='*50}")

            combined_df = pd.concat(all_datasets, ignore_index=True)

            # Save combined dataset
            output_file = f"{self.processed_dir}/extended_historical_combined_data.csv"
            combined_df.to_csv(output_file, index=False)

            # Generate summary report
            summary_report = self._generate_extended_summary_report(combined_df, collection_summary)

            logger.info(f"✓ Combined dataset saved: {len(combined_df)} total records")
            logger.info(f"✓ Summary report generated")

            return combined_df, summary_report
        else:
            logger.warning("No data collected from any extended sources")
            return pd.DataFrame(), "No data collected from extended sources"

    def _generate_extended_summary_report(self, combined_df, collection_summary):
        """Generate comprehensive summary report for extended historical data"""

        report_lines = [
            "EXTENDED HISTORICAL CRYPTOCURRENCY DATA COLLECTION REPORT",
            "=" * 60,
            f"Collection Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Date Range: {self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}",
            f"Total Years Covered: {(self.end_date - self.start_date).days / 365.25:.1f} years",
            "",
            "COLLECTION SUMMARY BY SOURCE:",
            "-" * 40
        ]

        for summary in collection_summary:
            report_lines.append(f"  {summary}")

        if not combined_df.empty:
            report_lines.extend([
                "",
                "COMBINED DATASET STATISTICS:",
                "-" * 40,
                f"Total Records: {len(combined_df):,}",
                f"Unique Symbols: {combined_df['symbol'].nunique() if 'symbol' in combined_df.columns else 'N/A'}",
                f"Date Range in Data: {combined_df['date'].min() if 'date' in combined_df.columns else 'N/A'} to {combined_df['date'].max() if 'date' in combined_df.columns else 'N/A'}",
                ""
            ])

            # Source breakdown
            if 'source' in combined_df.columns:
                report_lines.extend([
                    "RECORDS BY SOURCE:",
                    "-" * 20
                ])
                source_counts = combined_df['source'].value_counts()
                for source, count in source_counts.items():
                    report_lines.append(f"  {source}: {count:,} records")

        report_text = "\n".join(report_lines)

        # Save report
        report_file = f"{self.processed_dir}/extended_historical_collection_report.txt"
        with open(report_file, 'w') as f:
            f.write(report_text)

        return report_text


if __name__ == "__main__":
    collector = ExtendedHistoricalCollector()

    print("Starting Extended Historical Cryptocurrency Data Collection")
    print("=" * 60)
    print("This will collect 12-13 years of historical data from:")
    print("- Bitcoincharts.com (2011-2013 Bitcoin data)")
    print("- Early altcoins (Litecoin, Namecoin, etc.)")
    print("- Kaggle Bitcoin historical dataset")
    print("- GitHub cryptocurrency datasets")
    print("- Wayback Machine CoinMarketCap archives")
    print("- Blockchain.info historical charts")
    print()

    combined_data, summary = collector.collect_all_extended_sources()

    print("\n" + "=" * 60)
    print("EXTENDED COLLECTION COMPLETE!")
    print("=" * 60)
    print(summary)
